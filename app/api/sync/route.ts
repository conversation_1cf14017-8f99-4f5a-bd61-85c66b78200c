import { patientReq } from '@/lib/ccRequest';

export const GET = async (request: Request) => {
  const { searchParams } = new URL(request.url);
  const params: {
    page?: number;
    perPage?: number;
    active?: boolean;
    sort?: string;
  } = {
    page: searchParams.get('page') ? parseInt(searchParams.get('page') as string) : 1,
    perPage: searchParams.get('perPage') ? parseInt(searchParams.get('perPage') as string) : 20,
    active: searchParams.get('active') ? searchParams.get('active') === 'true' : true,
    sort: "-createdAt",
  }
  const patients = await patientReq.all(params);
  return Response.json(patients);
};
